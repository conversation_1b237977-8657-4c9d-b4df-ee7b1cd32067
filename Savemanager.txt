using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace JyGame
{
	// Token: 0x0200025F RID: 607
	public class SaveManager
	{
		// Token: 0x170001F1 RID: 497
		// (get) Token: 0x06001628 RID: 5672 RVA: 0x000B7B40 File Offset: 0x000B5D40
		public static string SaveDir
		{
			get
			{
				string text = string.Empty;
				text = ((GlobalData.CurrentMod != null) ? (CommonSettings.persistentDataPath + "/suyu/" + GlobalData.CurrentMod.key + "/saves/") : (CommonSettings.persistentDataPath + "/saves/"));
				if (!Directory.Exists(text))
				{
					Directory.CreateDirectory(text);
				}
				return text;
			}
		}

		// Token: 0x06001629 RID: 5673 RVA: 0x000B7BA0 File Offset: 0x000B5DA0
		public static string GetSave(string saveName)
		{
			RuntimeData.Instance.LastLoadingTime = new TimeSpan(DateTime.Now.Ticks);
			string text = SaveManager.SaveDir + saveName;
			bool flag = !File.Exists(text);
			string text2;
			if (flag)
			{
				text2 = string.Empty;
			}
			else
			{
				ModData.Load();
				using (StreamReader streamReader = new StreamReader(text))
				{
					string text3 = streamReader.ReadToEnd();
					bool flag2 = CommonSettings.EN_SAVE() != 0;
					if (flag2)
					{
						text2 = SaveManager.GetDecode(text3, false);
					}
					else
					{
						text2 = text3;
					}
				}
			}
			return text2;
		}

		// Token: 0x0600162A RID: 5674 RVA: 0x000B7C44 File Offset: 0x000B5E44
		public static void SetSave(string saveName, string content)
		{
			using (StreamWriter streamWriter = new StreamWriter(SaveManager.SaveDir + saveName))
			{
				bool flag = CommonSettings.EN_SAVE() != 0;
				if (flag)
				{
					content = SaveManager.SetEncrypt(content);
				}
				streamWriter.Write(content);
			}
			bool flag2 = !(saveName == "autosave") || RuntimeData.Instance.AutoSaveOnly;
			if (flag2)
			{
				ModData.Save();
			}
		}

		// Token: 0x0600162B RID: 5675 RVA: 0x000B7CC8 File Offset: 0x000B5EC8
		public static void DeleteSave(string saveName)
		{
			string text = SaveManager.SaveDir + saveName;
			bool flag = File.Exists(text);
			if (flag)
			{
				File.Delete(text);
			}
		}

		// Token: 0x0600162C RID: 5676 RVA: 0x000B7CF8 File Offset: 0x000B5EF8
		public static bool ExistSave(string saveName)
		{
			string text = SaveManager.SaveDir + saveName;
			return File.Exists(text);
		}

		// Token: 0x0600162D RID: 5677 RVA: 0x000B7D1C File Offset: 0x000B5F1C
		public static string crcjm(string input)
		{
			string text = SaveManager.jm(input);
			string text2 = SaveManager.CRC16_C(input);
			return text2 + "@" + text;
		}

		// Token: 0x0600162E RID: 5678 RVA: 0x000B7D48 File Offset: 0x000B5F48
		public static string crcm(string input)
		{
			string[] array = input.Split(new char[] { '@' });
			bool flag = array.Length != 2;
			string text;
			if (flag)
			{
				text = string.Empty;
			}
			else
			{
				string text2 = array[0];
				string text3 = SaveManager.m(array[1]);
				string text4 = SaveManager.CRC16_C(text3);
				bool flag2 = text2 != text4;
				if (flag2)
				{
					text = string.Empty;
				}
				else
				{
					text = text3;
				}
			}
			return text;
		}

		// Token: 0x0600162F RID: 5679 RVA: 0x000B7DB4 File Offset: 0x000B5FB4
		private static string jm(string Message)
		{
			UTF8Encoding utf8Encoding = new UTF8Encoding();
			MD5CryptoServiceProvider md5CryptoServiceProvider = new MD5CryptoServiceProvider();
			byte[] array = md5CryptoServiceProvider.ComputeHash(utf8Encoding.GetBytes("Yh$45Ct@mods"));
			TripleDESCryptoServiceProvider tripleDESCryptoServiceProvider = new TripleDESCryptoServiceProvider
			{
				Key = array,
				Mode = CipherMode.ECB,
				Padding = PaddingMode.PKCS7
			};
			byte[] bytes = utf8Encoding.GetBytes(Message);
			byte[] array2;
			try
			{
				array2 = tripleDESCryptoServiceProvider.CreateEncryptor().TransformFinalBlock(bytes, 0, bytes.Length);
			}
			finally
			{
				tripleDESCryptoServiceProvider.Clear();
				md5CryptoServiceProvider.Clear();
			}
			return Convert.ToBase64String(array2);
		}

		// Token: 0x06001630 RID: 5680 RVA: 0x000B7E54 File Offset: 0x000B6054
		private static string m(string Message)
		{
			UTF8Encoding utf8Encoding = new UTF8Encoding();
			MD5CryptoServiceProvider md5CryptoServiceProvider = new MD5CryptoServiceProvider();
			byte[] array = md5CryptoServiceProvider.ComputeHash(utf8Encoding.GetBytes("Yh$45Ct@mods"));
			TripleDESCryptoServiceProvider tripleDESCryptoServiceProvider = new TripleDESCryptoServiceProvider
			{
				Key = array,
				Mode = CipherMode.ECB,
				Padding = PaddingMode.PKCS7
			};
			byte[] array2 = Convert.FromBase64String(Message);
			byte[] array3;
			try
			{
				array3 = tripleDESCryptoServiceProvider.CreateDecryptor().TransformFinalBlock(array2, 0, array2.Length);
			}
			finally
			{
				tripleDESCryptoServiceProvider.Clear();
				md5CryptoServiceProvider.Clear();
			}
			return utf8Encoding.GetString(array3);
		}

		// Token: 0x06001631 RID: 5681 RVA: 0x000B7EF4 File Offset: 0x000B60F4
		private static string CRC16_C(string str)
		{
			byte[] bytes = Encoding.UTF8.GetBytes(str);
			byte b = byte.MaxValue;
			byte b2 = byte.MaxValue;
			byte b3 = 1;
			byte b4 = 160;
			byte[] array = bytes;
			for (int i = 0; i < array.Length; i++)
			{
				b ^= array[i];
				for (int j = 0; j <= 7; j++)
				{
					byte b5 = b2;
					byte b6 = b;
					b2 = (byte)(b2 >> 1);
					b = (byte)(b >> 1);
					bool flag = (b5 & 1) == 1;
					if (flag)
					{
						b |= 128;
					}
					bool flag2 = (b6 & 1) == 1;
					if (flag2)
					{
						b2 ^= b4;
						b ^= b3;
					}
				}
			}
			return string.Format("{0}{1}", b2, b);
		}

		// Token: 0x06001632 RID: 5682 RVA: 0x000B7FC4 File Offset: 0x000B61C4
		private static string Encrypt(Encoding encode, string source)
		{
			byte[] bytes = encode.GetBytes(source);
			string text;
			try
			{
				text = Convert.ToBase64String(bytes);
			}
			catch
			{
				text = source;
			}
			return text;
		}

		// Token: 0x06001633 RID: 5683 RVA: 0x000B8004 File Offset: 0x000B6204
		private static string Decode(Encoding encode, string result)
		{
			string text;
			try
			{
				byte[] array = Convert.FromBase64String(result);
				text = encode.GetString(array);
			}
			catch
			{
				text = string.Empty;
			}
			return text;
		}

		// Token: 0x06001634 RID: 5684 RVA: 0x000B8048 File Offset: 0x000B6248
		private static string getResult(string zz)
		{
			char[] array = new char[]
			{
				'Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P',
				'A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', 'Z',
				'X', 'C', 'V', 'B', 'N', 'M'
			};
			char[] array2 = new char[]
			{
				'q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p',
				'a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'z',
				'x', 'c', 'v', 'b', 'n', 'm'
			};
			StringBuilder stringBuilder = new StringBuilder();
			for (int i = 0; i < zz.Length; i++)
			{
				bool flag = Array.IndexOf<char>(array, zz[i]) == -1;
				if (flag)
				{
					bool flag2 = Array.IndexOf<char>(array2, zz[i]) == -1;
					if (flag2)
					{
						stringBuilder.Append(zz[i]);
					}
					else
					{
						for (int j = 0; j < array2.Length; j++)
						{
							bool flag3 = array2[j] == zz[i];
							if (flag3)
							{
								stringBuilder.Append(array[j]);
								break;
							}
						}
					}
				}
				else
				{
					for (int k = 0; k < array.Length; k++)
					{
						bool flag4 = array[k] == zz[i];
						if (flag4)
						{
							stringBuilder.Append(array2[k]);
							break;
						}
					}
				}
			}
			return stringBuilder.ToString();
		}

		// Token: 0x06001635 RID: 5685 RVA: 0x000B8164 File Offset: 0x000B6364
		public static string Encrypt_Save(string source)
		{
			string text = SaveManager.Encrypt(Encoding.UTF8, source);
			bool flag = text.Substring(text.Length - 1, 1) == "=";
			if (flag)
			{
				text = text.Substring(0, text.Length - 1) + "@";
			}
			text = "@" + text;
			int randomInt = Tools.GetRandomInt(3, 9);
			for (int i = 0; i < randomInt; i++)
			{
				int randomInt2 = Tools.GetRandomInt(1, text.Length - 2);
				text = text.Insert(randomInt2, "#");
			}
			text = text.Replace("/", "$");
			return SaveManager.getResult(text);
		}

		// Token: 0x06001636 RID: 5686 RVA: 0x000B821C File Offset: 0x000B641C
		public static string Decode_Save(string result)
		{
			bool flag = result.Substring(result.Length - 1, 1) == "@";
			if (flag)
			{
				result = result.Substring(0, result.Length - 1) + "=";
			}
			result = result.Substring(1, result.Length - 1);
			result = result.Replace("#", "").Replace("$", "/");
			result = SaveManager.getResult(result);
			return SaveManager.Decode(Encoding.UTF8, result);
		}

		// Token: 0x06001637 RID: 5687 RVA: 0x000B82B0 File Offset: 0x000B64B0
		public static string ExtractString(string str)
		{
			str = SaveManager.Decode(Encoding.UTF8, str.Replace('\\', '0').Replace('_', '1').Substring(1, str.Length - 2)
				.Replace("/", "")
				.Replace("#", "/"));
			StringBuilder stringBuilder = new StringBuilder();
			for (int i = 0; i < str.Length; i += 2)
			{
				int num = (int)(str[i + 1] - '0');
				for (int j = 0; j < num; j++)
				{
					stringBuilder.Append(str[i]);
				}
			}
			return stringBuilder.ToString();
		}

		// Token: 0x06001638 RID: 5688 RVA: 0x000B8364 File Offset: 0x000B6564
		public static string GetSaveForList(string saveName)
		{
			string text = SaveManager.SaveDir + saveName;
			bool flag = !File.Exists(text);
			string text2;
			if (flag)
			{
				text2 = string.Empty;
			}
			else
			{
				using (StreamReader streamReader = new StreamReader(text))
				{
					string text3 = streamReader.ReadToEnd();
					bool flag2 = SavePanelUI.EN_SAVE() != 0;
					if (flag2)
					{
						text2 = SaveManager.GetDecode(text3, true);
					}
					else
					{
						bool flag3 = text3.StartsWith("@");
						if (flag3)
						{
							text2 = string.Empty;
						}
						else
						{
							text2 = text3;
						}
					}
				}
			}
			return text2;
		}

		// Token: 0x06001639 RID: 5689 RVA: 0x000B83FC File Offset: 0x000B65FC
		private static string SetEncrypt(string content)
		{
			int num = content.IndexOf('>');
			string text = content.Substring(0, num + 1);
			int num2 = content.LastIndexOf('<');
			string text2 = content.Substring(num2, content.Length - num2);
			content = content.Substring(num + 1, num2 - num - 1);
			content = SaveManager.Encrypt_Save(content);
			return text + content + text2;
		}

		// Token: 0x0600163A RID: 5690 RVA: 0x000B8460 File Offset: 0x000B6660
		private static string GetDecode(string content, bool forList = false)
		{
			string text = string.Empty;
			int num = content.IndexOf('>');
			bool flag = num != -1;
			if (flag)
			{
				text = content.Substring(0, num + 1);
			}
			string text2 = string.Empty;
			bool flag2 = forList && text.IndexOf("information=") > 22;
			if (flag2)
			{
				content = string.Empty;
			}
			else
			{
				int num2 = content.LastIndexOf('<');
				bool flag3 = num2 != -1;
				if (flag3)
				{
					text2 = content.Substring(num2, content.Length - num2);
				}
				bool flag4 = num != -1 && num2 != -1;
				if (flag4)
				{
					content = content.Substring(num + 1, num2 - num - 1);
				}
				content = SaveManager.Decode_Save(content);
			}
			return text + content + text2;
		}
	}
}
