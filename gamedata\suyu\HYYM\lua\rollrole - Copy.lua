﻿--[[金庸群侠传X

ROLL扩展逻辑]]--

local AudioManager = luanet.import_type('JyGame.AudioManager')
local LuaTool = luanet.import_type('JyGame.LuaTool')
local Debug = luanet.import_type('UnityEngine.Debug')
local Item = luanet.import_type('JyGame.Item')
local CommonSettings = luanet.import_type('JyGame.CommonSettings')
local RuntimeData = luanet.import_type('JyGame.RuntimeData')
local Role = luanet.import_type('JyGame.Role')

local ROLLROLE_AniList={
--"头像.nvwujiang","头像.nvwujiang1","头像.yinqiang",
--这一行是自定义的模型头像
--后边96个是全内置模型
"头像.hongyinvxia2" ,"头像.xiaonvhai" ,"头像.nvjianzi" ,
}
--开场问题
local ROLLROLE_QUESTIONS = {
	[0]={title="在开始游戏前，请您回答几个问题", opts={"继续.."}},
	[1]={title="请慎重选择游戏难度", opts={}},
	[2]={title="请输入你的姓氏", opts={"输入姓氏，最多两字"}},
	[3]={title="请输入你的名字", opts={"输入名字，最多两字"}},
}

local ROLLTEAM_QUESTIONS = {
	[0]={title="功能测试", opts={"继续.."}},
	[1]={title="请选择你要重置的角色", opts={}},
	[2]={title="请重置角色名字", opts={"继续.."}},
}
local ROLLTEAM={}
local count=RuntimeData.Instance.Team.Count
--可选头像
local ROLLROLE_HeadList = {
	"头像.主角","头像.主角2",
}

local ROLLTEAM_HeadList = {
	"头像.主角","头像.主角2",
}

--奖励的队友
local ROLLROLE_BonusRole = {
	[0]={},
	[1]={"姬雪"},
	[2]={"姬雪"},
	[3]={"姬雪"},
	[4]={"姬雪"},
	[5]={"姬雪"},
	[6]={"姬雪"},
	[7]={"姬雪"},
	[8]={"姬雪"},
	[9]={"姬雪"}
}

--入口函数
function ROLLROLE_start(this)
AudioManager.Instance:Play("音乐.纵横天下")
	--开始游戏播放音乐
	--清楚历史结果
	this.results:Clear()
	
	--初始化难度选择
	
	--以前是无悔，并且大于一周目，这里只有一个选项
	ROLLROLE_QUESTIONS[1].opts = {}
	
	if(RuntimeData.Instance.AutoSaveOnly and RuntimeData.Instance.Round > 1) then
		table.insert(ROLLROLE_QUESTIONS[1].opts, "<color='magenta'>无悔 | 难度极高，实时存档自虐专用，无法多周目</color>")
	else
		if(RuntimeData.Instance.Round == 1) then
			table.insert(ROLLROLE_QUESTIONS[1].opts, "简单 | 难度较低，适合新手或剧情党，仅一周目可选")
		end
		table.insert(ROLLROLE_QUESTIONS[1].opts, "<color='yellow'>标准 | 难度适中，正常体验游戏内容，可进行多周目</color>")
		table.insert(ROLLROLE_QUESTIONS[1].opts, "<color='red'>困难 | 难度极高，仅适合骨灰级玩家，可进行多周目</color>")
		
		if(RuntimeData.Instance.Round == 1) then
			table.insert(ROLLROLE_QUESTIONS[1].opts, "<color='magenta'>无悔 | 难度极高，实时存档自虐专用，仅一周目可选</color>")
		end
	end
	
	--开始选项
	ROLLROLE_LoadSelection(this, 0)
end

function ROLLROLE_getBonusRole(index)
	return LuaTool.MakeStringArray(ROLLROLE_BonusRole[index])
end

function ROLLROLE_LoadSelection(this, index)
    local question = ROLLROLE_QUESTIONS[index]
    if question == nil then
        ROLLROLE_InputSurname(this)
    else
        -- 显示当前问题
        this:LoadSelection(question.title, question.opts, function(selectIndex)
            -- 将用户选择的选项加入结果
            this.results:Add(selectIndex)

            if index == 2 then
                ROLLROLE_InputSurname(this)
            elseif index == 3 then
                ROLLROLE_InputName(this)
            else
                ROLLROLE_LoadSelection(this, index + 1)
            end
        end)
    end
end


function ROLLROLE_InputSurname(this)
    -- 输入姓氏
    this.nameInputPanel:Show("慕", LuaTool.MakeStringCallBack(function(femaleName)

        RuntimeData.Instance.femaleName = femaleName

        ROLLROLE_LoadSelection(this, 3)
    end))
end


function ROLLROLE_InputName(this)
    -- 输入名字
    this.nameInputPanel:Show("青璇", LuaTool.MakeStringCallBack(function(newName)

        RuntimeData.Instance.newName = newName

        -- 显示头像选择面板，继续后续步骤
        this.headSelectPanelObj:SetActive(true)
        this.headSelectMenu:Show(LuaTool.MakeStringArray(ROLLROLE_HeadList), LuaTool.MakeStringCallBack(function(selectKey)
            this.selectHeadKey = selectKey
            this.headSelectPanelObj:SetActive(false)
            -- 处理头像字符串，去掉前缀
            str_ani = string.char(string.byte(selectKey, 8, -1))
            this:LoadSelection("欢迎进入红颜一梦的世界", {"请选择角色初始属性"}, function(selectIndex)
                ROLLROLE_Reset(this)
            end)
        end))
    end))
end



function ROLLROLE_Callani(this)
    this.headSelectPanelObj:SetActive(true)
    this.headSelectMenu:Show(LuaTool.MakeStringArray(ROLLROLE_AniList), LuaTool.MakeStringCallBack(function(selectKey)
        this.headSelectPanelObj:SetActive(false)
        --str_ani=string.gsub(selectKey,"头像.","",1)
        str_ani=string.char(string.byte(selectKey,8,-1))
        --字符窜处理基础函数之一这里用来去掉返回值的前缀
        this:LoadSelection("欢迎进入红颜一梦的世界", {"选择角色初始属性"}, function(selectIndex)    
            ROLLROLE_Reset(this)
        end)
    end))
end

function ROLLROLE_Reset(this)
    --根据答案生成初始角色和物品
    ROLLROLE_MakeBeginningCondition(this)

    --随机调整
    ROLLROLE_MakeRandomCondition(this)
    
    --显示按钮
    this.RoleConfirmButtonObj:SetActive(true)
    this.RoleResetButtonObj:SetActive(true)
    this.rolePanelObj.transform:FindChild("CancelButton").gameObject:SetActive(false)
    
    --显示
    this.rolePanel:Show(this.makeRole)
end

function ROLLROLE_MakeBeginningCondition(this)
    local makeItems = this:GenerateEmptyItems()
    local makeRole = Role.Generate("主角")
    local makeMoney = 500
    
    makeRole.Head = this.selectHeadKey
    if str_ani ~= "none" then
        makeRole.Animation = str_ani
    end
    local fullName = (RuntimeData.Instance.femaleName or "") .. (RuntimeData.Instance.newName or "")
    makeRole.Name = fullName
    makeItems:Clear()
    makeItems:Add(Item.GetItem("小还丹"))
    makeItems:Add(Item.GetItem("小还丹"))
    makeItems:Add(Item.GetItem("大还丹"))
	
    local results = this.results
--RuntimeData.Instance.Team:Add(Role.Generate("阿青"))
	--选择你的游戏难度
    if(table.getn(ROLLROLE_QUESTIONS[1].opts)==4) then
        if(results[1]==0) then
            RuntimeData.Instance.GameMode = "normal"
            RuntimeData.Instance.FriendlyFire = false
            RuntimeData.Instance.AutoSaveOnly = false
        elseif(results[1]==1) then
            RuntimeData.Instance.GameMode = "hard"
            RuntimeData.Instance.FriendlyFire = true
            RuntimeData.Instance.AutoSaveOnly = false
        elseif(results[1]==2) then
            RuntimeData.Instance.GameMode = "crazy"
            RuntimeData.Instance.FriendlyFire = true
            RuntimeData.Instance.AutoSaveOnly = false
        elseif(results[1]==3) then
            RuntimeData.Instance.GameMode = "crazy"
            RuntimeData.Instance.FriendlyFire = true
            RuntimeData.Instance.AutoSaveOnly = true
        end
    elseif(table.getn(ROLLROLE_QUESTIONS[1].opts)==1) then
        RuntimeData.Instance.GameMode = "crazy"
        RuntimeData.Instance.FriendlyFire = true
        RuntimeData.Instance.AutoSaveOnly = true
    elseif(table.getn(ROLLROLE_QUESTIONS[1].opts)==2) then
        if(results[1]==0) then
            RuntimeData.Instance.GameMode = "hard"
            RuntimeData.Instance.FriendlyFire = true
            RuntimeData.Instance.AutoSaveOnly = false
        elseif(results[1]==1) then
            RuntimeData.Instance.GameMode = "crazy"
            RuntimeData.Instance.FriendlyFire = true
            RuntimeData.Instance.AutoSaveOnly = false
        end
    end
	
this.makeRole = makeRole
    this.makeItems = makeItems
    this.makeMoney = makeMoney
    
    -- Ensure team is initialized
    if RuntimeData.Instance.Team == nil then
        RuntimeData.Instance.Team = luanet.import_type('JyGame.Team').Create()
    else
        RuntimeData.Instance.Team:Clear() -- Clear previous team to avoid stale data
    end
    
    -- Add default team member
    RuntimeData.Instance.Team:Add(Role.Generate("南贤"))
    
    -- Call MakeZhoumuAndShilianBonus with error handling
    local status, err = pcall(function()
        this:MakeZhoumuAndShilianBonus()
    end)
    if not status then
        Debug.LogError("Error in MakeZhoumuAndShilianBonus: " .. tostring(err))
        -- Fallback: Set default money if the call fails
        this.makeMoney = 500
    end
    
    -- Cap money at 5000
    if this.makeMoney > 5000 then
        this.makeMoney = 5000
    end
end

function ROLLROLE_MakeRandomCondition(this)
    local randomAttr = {"gengu","bili","fuyuan","shenfa","dingli","wuxing","quanzhang","jianfa","daofa","qimen"}
    local randomAttrLength = table.getn(randomAttr)
    math.randomseed(tostring(os.time()):reverse():sub(1, 6))
    for i=1,3,1
    do
        local rnd = math.random(1, randomAttrLength)
        local attr = randomAttr[rnd]
        ROLLROLE_adjustAttr(this, this.makeRole, attr, 5)
    end
    for i=1,10,1
    do
        local rnd = math.random(1, randomAttrLength)
        local attr = randomAttr[rnd]
        ROLLROLE_adjustAttr(this, this.makeRole, attr, 1)
    end
end

function ROLLROLE_adjustAttr(this, makeRole, type, value)

	if (type == "hp") then
		makeRole.hp = makeRole.hp + value
	elseif (type == "maxhp") then
    	makeRole.maxhp = makeRole.maxhp + value
    elseif (type == "mp") then
    	makeRole.mp = makeRole.mp + value
    elseif (type == "maxmp") then	
    	makeRole.maxmp = makeRole.maxmp + value
    elseif (type == "gengu") then	
    	makeRole.gengu = makeRole.gengu + value
    elseif (type == "bili") then	
    	makeRole.bili = makeRole.bili + value
    elseif (type == "fuyuan") then
		makeRole.fuyuan = makeRole.fuyuan + value
    elseif (type == "shenfa") then
		makeRole.shenfa = makeRole.shenfa + value
    elseif (type == "dingli") then
		makeRole.dingli = makeRole.dingli + value
    elseif (type == "wuxing") then
		makeRole.wuxing = makeRole.wuxing + value
    elseif (type == "quanzhang") then
		makeRole.quanzhang = makeRole.quanzhang + value
    elseif (type == "jianfa") then
		makeRole.jianfa = makeRole.jianfa + value
    elseif (type == "daofa") then
		makeRole.daofa = makeRole.daofa + value
    elseif (type == "qimen") then
		makeRole.qimen = makeRole.qimen + value
	end

end

function ROLLROLE_NEXT_ROUND_ITEMS(this, num)
	--霹雳堂过关后，下周目送的秘籍书等物品。每个物品之间用#分隔。
	if (num <= 5) then
		return ""  
--返回字符串""里边的内容自行填写
--物品名称池，用#为分隔符
--示例:某些消耗品
	elseif (num > 5 and num <= 10) then
		return ""
--物品名称池，用#为分隔符
--示例:某些永久提升道具
	elseif (num > 10 and num <= 15) then
		return ""  
--物品名称池，用#为分隔符
--示例:天赋书
	elseif (num > 15 and num <= 20) then
		return "" 
--物品名称池，用#为分隔符
 --示例:特技书
	elseif (num > 20 and num <= 25) then
		return ""  
--物品名称池，用#为分隔符
--示例:外功书
	elseif (num > 25 and num <= 30) then
		return ""  
--物品名称池，用#为分隔符
--示例:内功书
	elseif (num > 30) then
		return ""
--物品名称池，用#为分隔符
--霹雳堂终极奖励
	end
	
	return ""
end
