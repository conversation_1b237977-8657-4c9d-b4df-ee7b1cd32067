using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text;
using LuaInterface;
using UnityEngine;

namespace JyGame
{
	// Token: 0x02000225 RID: 549
	public static class LuaManager
	{
		// Token: 0x06001395 RID: 5013 RVA: 0x0009DFF0 File Offset: 0x0009C1F0
		public static byte[] JyGameLuaLoader(string path)
		{
			byte[] array;
			if (CommonSettings.MOD_KEY() >= 0)
			{
				if (path.StartsWith("jygame/"))
				{
					string text = ModManager.ModBaseUrlPath + "lua/" + path.Replace("jygame/", string.Empty);
					global::UnityEngine.Debug.Log("loading lua file : " + text);
					using (StreamReader streamReader = new StreamReader(text))
					{
						string text2;
						if (GlobalData.CurrentMod.enc)
						{
							text2 = ((!GlobalData.CurrentMod.oldenc) ? SaveManager.ExtractString(streamReader.ReadToEnd()) : SaveManager.crcm(streamReader.ReadToEnd()));
						}
						else
						{
							text2 = streamReader.ReadToEnd();
							if (text2.Length > 100 && text2.StartsWith("@"))
							{
								text2 = SaveManager.ExtractString(text2);
							}
						}
						return new UTF8Encoding(true).GetBytes(text2);
					}
				}
				string text3 = "TextAssets/lua/" + path;
				global::UnityEngine.Debug.Log("loading lua file : " + text3);
				array = Resource.GetBytes("TextAssets/lua/" + path, false);
			}
			else
			{
				string text4 = "TextAssets/lua/" + path;
				global::UnityEngine.Debug.Log("loading lua file : " + text4);
				array = Resource.GetBytes("TextAssets/lua/" + path, false);
			}
			return array;
		}

		// Token: 0x06001396 RID: 5014 RVA: 0x000063E8 File Offset: 0x000045E8
		public static void Reload()
		{
			TriggerLogic.ClearluaExtensionConditions();
			LuaManager._luaConfig = null;
			LuaManager.Init(true);
		}

		// Token: 0x06001397 RID: 5015 RVA: 0x0009E144 File Offset: 0x0009C344
		public static void Init(bool forceReset = false)
		{
			if (forceReset)
			{
				LuaManager._inited = false;
				if (LuaManager._lua != null)
				{
					LuaManager._lua.Destroy();
				}
			}
			if (!LuaManager._inited)
			{
				LuaManager._lua = new LuaScriptMgr();
				LuaManager._lua.Start();
				try
				{
					foreach (string text in LuaManager.files)
					{
						LuaManager._lua.DoFile("jygame/" + text);
					}
				}
				catch (Exception ex)
				{
					global::UnityEngine.Debug.LogError(ex.ToString());
					FileLogger.instance.LogError("============LUA语法错误！===========");
					FileLogger.instance.LogError(ex.ToString());
				}
				LuaManager._inited = true;
				LuaTable luaTable = LuaManager.Call<LuaTable>("ROOT_getLuaFiles", new object[0]);
				try
				{
					foreach (object obj in luaTable.Values)
					{
						string text2 = (string)obj;
						LuaManager._lua.DoFile("jygame/" + text2);
					}
				}
				catch (Exception ex2)
				{
					global::UnityEngine.Debug.LogError(ex2.ToString());
					FileLogger.instance.LogError("============LUA语法错误！===========");
					FileLogger.instance.LogError(ex2.ToString());
				}
			}
		}

		// Token: 0x06001398 RID: 5016 RVA: 0x0009E2B4 File Offset: 0x0009C4B4
		public static object[] Call(string functionName, params object[] paras)
		{
			if (!LuaManager._inited)
			{
				LuaManager.Init(false);
			}
			LuaFunction luaFunction = LuaManager._lua.GetLuaFunction(functionName);
			object[] array;
			if (luaFunction == null)
			{
				global::UnityEngine.Debug.LogError("调用了未定义的lua 函数:" + functionName);
				global::UnityEngine.Debug.LogError(new StackTrace().ToString());
				array = null;
			}
			else
			{
				array = luaFunction.Call(paras);
			}
			return array;
		}

		// Token: 0x06001399 RID: 5017 RVA: 0x0009E30C File Offset: 0x0009C50C
		public static T Call<T>(string functionName, params object[] paras)
		{
			if (!LuaManager._inited)
			{
				LuaManager.Init(false);
			}
			LuaFunction luaFunction = LuaManager._lua.GetLuaFunction(functionName);
			T t;
			if (luaFunction == null)
			{
				global::UnityEngine.Debug.LogError("调用了未定义的lua 函数:" + functionName);
				global::UnityEngine.Debug.LogError(new StackTrace().ToString());
				t = default(T);
			}
			else
			{
				object[] array = luaFunction.Call(paras);
				if (array.Length == 0 || (array[0] is bool && !(bool)array[0]))
				{
					t = default(T);
				}
				else
				{
					t = (T)((object)array[0]);
				}
			}
			return t;
		}

		// Token: 0x0600139A RID: 5018 RVA: 0x0009E394 File Offset: 0x0009C594
		public static int CallWithIntReturn(string functionName, params object[] paras)
		{
			if (!LuaManager._inited)
			{
				LuaManager.Init(false);
			}
			LuaFunction luaFunction = LuaManager._lua.GetLuaFunction(functionName);
			int num;
			if (luaFunction == null)
			{
				global::UnityEngine.Debug.LogError("调用了未定义的lua 函数:" + functionName);
				global::UnityEngine.Debug.LogError(new StackTrace().ToString());
				num = -1;
			}
			else
			{
				num = Convert.ToInt32(luaFunction.Call(paras)[0]);
			}
			return num;
		}

		// Token: 0x0600139B RID: 5019 RVA: 0x0009E3F0 File Offset: 0x0009C5F0
		public static T GetConfig<T>(string key)
		{
			if (LuaManager._luaConfig == null)
			{
				LuaTable luaTable = LuaManager.Call<LuaTable>("ROOT_getConfigList", new object[0]);
				LuaManager._luaConfig = new Dictionary<string, object>();
				foreach (object obj in luaTable)
				{
					DictionaryEntry dictionaryEntry = (DictionaryEntry)obj;
					LuaManager._luaConfig.Add(dictionaryEntry.Key.ToString(), dictionaryEntry.Value);
				}
			}
			return (T)((object)LuaManager._luaConfig[key]);
		}

		// Token: 0x0600139C RID: 5020 RVA: 0x000063FB File Offset: 0x000045FB
		public static string GetConfig(string key)
		{
			return LuaManager.GetConfig<string>(key);
		}

		// Token: 0x0600139D RID: 5021 RVA: 0x00006403 File Offset: 0x00004603
		public static int GetConfigInt(string key)
		{
			return Convert.ToInt32(LuaManager.GetConfig<object>(key));
		}

		// Token: 0x0600139E RID: 5022 RVA: 0x00006410 File Offset: 0x00004610
		public static double GetConfigDouble(string key)
		{
			return Convert.ToDouble(LuaManager.GetConfig<object>(key));
		}

		// Token: 0x0600139F RID: 5023 RVA: 0x0009E490 File Offset: 0x0009C690
		public static string CallWithStringReturn(string functionName, params object[] paras)
		{
			if (!LuaManager._inited)
			{
				LuaManager.Init(false);
			}
			LuaFunction luaFunction = LuaManager._lua.GetLuaFunction(functionName);
			string text;
			if (luaFunction == null)
			{
				global::UnityEngine.Debug.LogError("调用了未定义的lua 函数:" + functionName);
				global::UnityEngine.Debug.LogError(new StackTrace().ToString());
				text = string.Empty;
			}
			else
			{
				text = Convert.ToString(luaFunction.Call(paras)[0]);
			}
			return text;
		}

		// Token: 0x060013A0 RID: 5024 RVA: 0x0000641D File Offset: 0x0000461D
		public static void null_luaConfig()
		{
			LuaManager._luaConfig = null;
		}

		// Token: 0x060013A1 RID: 5025 RVA: 0x0009E4F0 File Offset: 0x0009C6F0
		public static float CallWithFloatReturn(string functionName, params object[] paras)
		{
			if (!LuaManager._inited)
			{
				LuaManager.Init(false);
			}
			LuaFunction luaFunction = LuaManager._lua.GetLuaFunction(functionName);
			float num;
			if (luaFunction == null)
			{
				global::UnityEngine.Debug.LogError("调用了未定义的lua 函数:" + functionName);
				global::UnityEngine.Debug.LogError(new StackTrace().ToString());
				num = 0f;
			}
			else
			{
				num = (float)Convert.ToDouble(luaFunction.Call(paras)[0]);
			}
			return num;
		}

		// Token: 0x04000858 RID: 2136
		private static string[] files = new string[] { "main.lua", "test.lua" };

		// Token: 0x04000859 RID: 2137
		private static bool _inited = false;

		// Token: 0x0400085A RID: 2138
		public static LuaScriptMgr _lua;

		// Token: 0x0400085B RID: 2139
		private static Dictionary<string, object> _luaConfig;
	}
}
