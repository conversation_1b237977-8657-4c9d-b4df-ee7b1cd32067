<?xml version="1.0" encoding="utf-8"?>

<configuration>

	<configSections>
		<section name="appSettings" type="System.Configuration.NameValueFileSectionHandler, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		<section name="system.diagnostics" type="System.Diagnostics.DiagnosticsConfigurationHandler, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		<section name="system.runtime.remoting" type="System.Configuration.IgnoreSectionHandler, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowLocation="false"/>
		<section name="system.windows.forms" type="System.Configuration.IgnoreSectionHandler, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		<sectionGroup name="system.web">
			<section name="httpHandlers"
				 type="System.Web.Configuration.HttpHandlersSectionHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="httpModules"
				 type="System.Web.Configuration.HttpModulesConfigurationHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="machineKey"
				 type="System.Web.Configuration.MachineKeyConfigHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="authentication"
				 type="System.Web.Configuration.AuthenticationConfigHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="authorization"
				 type="System.Web.Configuration.AuthorizationConfigHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="globalization"
				 type="System.Web.Configuration.GlobalizationConfigurationHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="sessionState"
				 type="System.Web.SessionState.SessionStateSectionHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="trace"
				 type="System.Web.Configuration.TraceConfigurationHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="webServices"
				 type="System.Web.Services.Configuration.WebServicesConfigurationSectionHandler, System.Web.Services, Version=1.0.5000.0 Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="webControls"
				 type="System.Web.Configuration.WebControlsSectionHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="compilation"
				 type="System.Web.Configuration.CompilationConfigurationHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="httpRuntime"
				 type="System.Web.Configuration.HttpRuntimeConfigurationHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="customErrors"
				 type="System.Web.Configuration.CustomErrorsConfigHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<section name="pages"
				 type="System.Web.Configuration.PagesConfigurationHandler, System.Web, Version=1.0.5000.0, Culture=neutral" />
			<section name="clientTarget"
				 type="System.Web.Configuration.ClientTargetSectionHandler, System.Web, Version=1.0.5000.0, Culture=neutral" />
		</sectionGroup>
		<sectionGroup name="system.net">
			<section name="settings"
				 type="System.Net.Configuration.NetConfigurationHandler, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<section name="connectionManagement"
				 type="System.Net.Configuration.ConnectionManagementHandler, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<section name="authenticationModules"
				 type="System.Net.Configuration.NetAuthenticationModuleHandler, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<section name="defaultProxy"
				 type="System.Net.Configuration.DefaultProxyHandler, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<section name="webRequestModules"
				 type="System.Net.Configuration.WebRequestModuleHandler, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		</sectionGroup>
		<section name="system.drawing" type="System.Configuration.NameValueSectionHandler, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
	</configSections>
	<system.net>
		<connectionManagement>
			<add address="*" maxconnection="2" />
		</connectionManagement>
		<authenticationModules>
			<add type="System.Net.BasicClient, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<add type="System.Net.DigestClient, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<add type="System.Net.NtlmClient, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		</authenticationModules>
		<defaultProxy>
			<proxy
				usesystemdefault = "true"
				bypassonlocal = "true"
			/>
			<!--  proxyaddress can also be set -->
		</defaultProxy>
		<webRequestModules>
			<add prefix="http" type="System.Net.HttpRequestCreator, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<add prefix="https" type="System.Net.HttpRequestCreator, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<add prefix="file" type="System.Net.FileWebRequestCreator, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		</webRequestModules>
		<settings>
			<ipv6 enabled="false"/>
		</settings>
	</system.net>
	<system.web>
		<httpHandlers>
			<add verb="*" path="Trace.axd" type="System.Web.Handlers.TraceHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<add verb="*" path="*.aspx" type="System.Web.UI.PageHandlerFactory, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<add verb="*" path="*.asmx" validate="false" type="System.Web.Services.Protocols.WebServiceHandlerFactory, System.Web.Services, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<add verb="*" path="*.ashx" type="System.Web.UI.SimpleHandlerFactory, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<add verb="GET" path="WebResource.axd" type="System.Web.Handlers.AssemblyResourceLoader, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<add verb="*" path="*.asax" type="System.Web.HttpForbiddenHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<add verb="*" path="*.ascx" type="System.Web.HttpForbiddenHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<add verb="*" path="*.config" type="System.Web.HttpForbiddenHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<add verb="*" path="*.Config" type="System.Web.HttpForbiddenHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<add verb="*" path="*.cs" type="System.Web.HttpForbiddenHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<add verb="*" path="*.dll" type="System.Web.HttpForbiddenHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
	      	<add verb="*" path="*.rem" type="System.Runtime.Remoting.Channels.Http.HttpRemotingHandlerFactory, System.Runtime.Remoting, Culture=neutral, PublicKeyToken=b77a5c561934e089" validate="false" />
      		<add verb="*" path="*.soap" type="System.Runtime.Remoting.Channels.Http.HttpRemotingHandlerFactory, System.Runtime.Remoting, Culture=neutral, PublicKeyToken=b77a5c561934e089" validate="false" />
			<add verb="GET,HEAD" path="*" type="System.Web.StaticFileHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<add verb="*" path="*" type="System.Web.HttpMethodNotAllowedHandler, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
		</httpHandlers>
		<httpModules>
			<add name="FormsAuthentication" type="System.Web.Security.FormsAuthenticationModule, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<add name="UrlAuthorization" type="System.Web.Security.UrlAuthorizationModule, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<add name="Session" type="System.Web.SessionState.SessionStateModule, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<add name="OutputCache" type="System.Web.Caching.OutputCacheModule, System.Web, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
		</httpModules>
		<authentication mode="Forms">
			<forms name=".MONOAUTH" loginUrl="login.aspx" protection="All" timeout="30" path="/">
				<credentials passwordFormat="Clear">
					<!--<user name="gonzalo" password="gonz"/>-->
				</credentials>
			</forms>
		</authentication>
		<machineKey validationKey="AutoGenerate" decryptionKey="AutoGenerate" validation="SHA1" />
		<globalization  requestEncoding="utf-8"
				responseEncoding="utf-8"
				fileEncoding="utf-8"/>
		<!--
				culture="en-US"
				uiculture="en-US" />
		-->
		<sessionState mode="InProc" />
		<webServices>
			<protocols>
				<add name="HttpSoap"/>
				<add name="HttpPost"/>
				<add name="HttpGet"/>
				<add name="Documentation"/>
			</protocols>
			<wsdlHelpGenerator href="DefaultWsdlHelpGenerator.aspx" />
		</webServices>
		<webControls clientScriptsLocation="/web_scripts" />
		<compilation debug="false" defaultLanguage="c#" explicit="true" strict="false" >
			<compilers>
			<compiler language="cs;c#;csharp" extension=".cs" warningLevel="1" compilerOptions=""
				  type="Microsoft.CSharp.CSharpCodeProvider, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<compiler language="vb;vbs;visualbasic;vbscript" extension=".vb"
				  warningLevel="1" compilerOptions=""
				  type="Microsoft.VisualBasic.VBCodeProvider, System, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			</compilers>
			<assemblies>
				<!--<add assembly="mscorlib" /> -->
				<add assembly="System" />
				<add assembly="System.Xml" />
				<add assembly="System.Data" />
				<add assembly="System.Web" />
				<add assembly="System.Web.Services" />
				<add assembly="System.Drawing" />
				<add assembly="*" /> <!-- Add assemblies in bin directory -->
			</assemblies>
		</compilation>
		<!-- 
			RequestLengthDiskThreshold is a 2.0 feature that we
			enable for 1.x too.
		-->
		<httpRuntime executionTimeout="90"
			     maxRequestLength="4096"
			     requestLengthDiskThreshold="256" 
			     useFullyQualifiedRedirectUrl="false"
			     minFreeThreads="8"
			     minLocalRequestFreeThreads="4"
			     appRequestQueueLimit="100" />
		<pages 	buffer="true"
			enableSessionState="true"
			autoEventWireup="true"
			validateRequest="true" />
		<clientTarget>
			<add alias="ie5" userAgent="Mozilla/4.0 (compatible; MSIE 5.5; Windows NT 4.0)" />
			<add alias="ie4" userAgent="Mozilla/4.0 (compatible; MSIE 4.0; Windows NT 4.0)" />
			<add alias="uplevel" userAgent="Mozilla/4.0 (compatible; MSIE 4.0; Windows NT 4.0)" />
			<add alias="downlevel" userAgent="Unknown" />
		</clientTarget>
	</system.web>
	
	<system.runtime.remoting>
		<application>
			<channels>
				<channel ref="http client" displayName="http client (delay loaded)" delayLoadAsClientChannel="true" />
				<channel ref="tcp client" displayName="tcp client (delay loaded)" delayLoadAsClientChannel="true" />
			</channels>
		</application>
		<channels>
			<channel id="http" type="System.Runtime.Remoting.Channels.Http.HttpChannel, System.Runtime.Remoting, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<channel id="http client" type="System.Runtime.Remoting.Channels.Http.HttpClientChannel, System.Runtime.Remoting, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<channel id="http server" type="System.Runtime.Remoting.Channels.Http.HttpServerChannel, System.Runtime.Remoting, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<channel id="tcp" type="System.Runtime.Remoting.Channels.Tcp.TcpChannel, System.Runtime.Remoting, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<channel id="tcp client" type="System.Runtime.Remoting.Channels.Tcp.TcpClientChannel, System.Runtime.Remoting, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			<channel id="tcp server" type="System.Runtime.Remoting.Channels.Tcp.TcpServerChannel, System.Runtime.Remoting, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		</channels>
			<channelSinkProviders>
				<clientProviders>
					<formatter id="soap" type="System.Runtime.Remoting.Channels.SoapClientFormatterSinkProvider, System.Runtime.Remoting, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
					<formatter id="binary" type="System.Runtime.Remoting.Channels.BinaryClientFormatterSinkProvider, System.Runtime.Remoting, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
				</clientProviders>
				<serverProviders>
					<formatter id="soap" type="System.Runtime.Remoting.Channels.SoapServerFormatterSinkProvider, System.Runtime.Remoting, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
					<formatter id="binary" type="System.Runtime.Remoting.Channels.BinaryServerFormatterSinkProvider, System.Runtime.Remoting, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
					<provider id="wsdl" type="System.Runtime.Remoting.MetadataServices.SdlChannelSinkProvider, System.Runtime.Remoting, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
				</serverProviders>
			</channelSinkProviders>
	</system.runtime.remoting>
	
	<appSettings>
	<!--<add key="yourkey" value="your value" /> -->
	<!--<remove key="a key defined higher in the hierarchy" /> -->
	<!--<clear/> Removes all defined settings -->
	</appSettings>
	<system.diagnostics>
		<trace autoflush="false" indentsize="4" />
	</system.diagnostics>
        <system.drawing>
        </system.drawing>

	<mscorlib>
		<cryptographySettings>
			<cryptoNameMapping>
				<cryptoClasses>
					<cryptoClass monoMD2="Mono.Security.Cryptography.MD2Managed, Mono.Security, Version=1.0.5000.0, Culture=neutral, PublicKeyToken=0738eb9f132ed756" />
				</cryptoClasses>
				<nameEntry name="MD2" class="monoMD2" />
			</cryptoNameMapping>
			<oidMap>
				<oidEntry OID="1.2.840.113549.2.2" name="MD2" />
				<oidEntry OID="1.2.840.113549.2.2" name="Mono.Security.Cryptography.MD2Managed" />
			</oidMap>
		</cryptographySettings>
	</mscorlib>

	<strongNames>
		<pubTokenMapping>
			<!-- ECMA key -->
			<map Token="b77a5c561934e089" PublicKey="002400000480000094000000060200000024000052534131000400000100010079159977d2d03a8e6bea7a2e74e8d1afcc93e8851974952bb480a12c9134474d04062447c37e0e68c080536fcf3c3fbe2ff9c979ce998475e506e8ce82dd5b0f350dc10e93bf2eeecf874b24770c5081dbea7447fddafa277b22de47d6ffea449674a4f9fccf84d15069089380284dbdd35f46cdff12a1bd78e4ef0065d016df" />
			<!-- Microsoft (final) key -->
			<map Token="b03f5f7f11d50a3a" PublicKey="002400000480000094000000060200000024000052534131000400000100010079159977d2d03a8e6bea7a2e74e8d1afcc93e8851974952bb480a12c9134474d04062447c37e0e68c080536fcf3c3fbe2ff9c979ce998475e506e8ce82dd5b0f350dc10e93bf2eeecf874b24770c5081dbea7447fddafa277b22de47d6ffea449674a4f9fccf84d15069089380284dbdd35f46cdff12a1bd78e4ef0065d016df" />
			<!-- Microsoft (Web Service Enhancement) key -->
			<map Token="31bf3856ad364e35" PublicKey="002400000480000094000000060200000024000052534131000400000100010079159977d2d03a8e6bea7a2e74e8d1afcc93e8851974952bb480a12c9134474d04062447c37e0e68c080536fcf3c3fbe2ff9c979ce998475e506e8ce82dd5b0f350dc10e93bf2eeecf874b24770c5081dbea7447fddafa277b22de47d6ffea449674a4f9fccf84d15069089380284dbdd35f46cdff12a1bd78e4ef0065d016df" />
			<!-- IBM (DB2 Data Provider) key -->
			<map Token="7c307b91aa13d208" PublicKey="002400000480000094000000060200000024000052534131000400000100010079159977d2d03a8e6bea7a2e74e8d1afcc93e8851974952bb480a12c9134474d04062447c37e0e68c080536fcf3c3fbe2ff9c979ce998475e506e8ce82dd5b0f350dc10e93bf2eeecf874b24770c5081dbea7447fddafa277b22de47d6ffea449674a4f9fccf84d15069089380284dbdd35f46cdff12a1bd78e4ef0065d016df" />
			<!-- Silverlight 2.0 key -->
			<map Token="7cec85d7bea7798e" PublicKey="002400000480000094000000060200000024000052534131000400000100010079159977d2d03a8e6bea7a2e74e8d1afcc93e8851974952bb480a12c9134474d04062447c37e0e68c080536fcf3c3fbe2ff9c979ce998475e506e8ce82dd5b0f350dc10e93bf2eeecf874b24770c5081dbea7447fddafa277b22de47d6ffea449674a4f9fccf84d15069089380284dbdd35f46cdff12a1bd78e4ef0065d016df" />
		</pubTokenMapping>
	</strongNames>
</configuration>


